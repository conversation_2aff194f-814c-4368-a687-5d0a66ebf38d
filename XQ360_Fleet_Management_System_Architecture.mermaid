graph TB
    %% External Systems and Databases
    subgraph "External Systems"
        SQL[SQL Server Database]
        Redis[Redis Cache]
        AzureIoT[Azure IoT Hub]
        AzureBlob[Azure Blob Storage]
        AppInsights[Application Insights]
    end

    %% Web Application Layer
    subgraph "Web Application Layer"
        subgraph "Generated Web Layer"
            WebApp[Web Application<br/>Program.cs]
            WebConfig[Web Configuration<br/>appsettings.json]
            UrlRewrite[URL Rewrite Rules<br/>UrlRewrite.xml]
        end
        
        subgraph "Custom Web Layer"
            CustomWeb[Custom Web Components<br/>wwwroot/]
            CustomStyles[Custom Styles<br/>ui-elements-linde.css]
        end
    end

    %% Service Layer
    subgraph "Service Layer"
        subgraph "Generated Service Layer"
            ApiControllers[API Controllers<br/>85+ Controllers]
            EntityControllers[Entity Controllers<br/>207+ Controllers]
            ServiceConfig[Service Configuration<br/>Container.cs]
        end
        
        subgraph "Custom Service Layer"
            CustomContainer[Custom Container<br/>Container.custom.cs]
            CustomMiddleware[Custom Middleware<br/>SubdomainMiddleware]
            CustomSecurity[Custom Security<br/>OAuth Providers]
        end
    end

    %% Business Layer
    subgraph "Business Layer"
        subgraph "Generated Business Layer"
            BusinessComponents[Business Components<br/>123+ Components]
            BusinessInterfaces[Business Interfaces<br/>200+ Interfaces]
            TaskRunner[Task Runner<br/>GOTaskRunner]
        end
        
        subgraph "Custom Business Layer"
            CustomBusiness[Custom Business<br/>Components]
            CustomTests[Custom Business Tests<br/>43 Test Files]
        end
    end

    %% Data Layer
    subgraph "Data Layer"
        subgraph "Generated Data Layer"
            DataObjects[Data Objects<br/>681+ Objects]
            DataProviders[Data Providers<br/>207+ Providers]
            NHibernate[NHibernate ORM<br/>166+ Mappings]
            DataFactories[Data Factories<br/>207+ Factories]
        end
        
        subgraph "Custom Data Layer"
            CustomDataObjects[Custom Data Objects<br/>15+ Objects]
            CustomProviders[Custom Data Providers<br/>40+ Providers]
            CustomExtensions[Data Provider Extensions<br/>40+ Extensions]
        end
    end

    %% Model Layer
    subgraph "Model Layer"
        subgraph "Generated Model Layer"
            ModelComponents[Model Components<br/>170+ Components]
            ModelProviders[Model Providers<br/>207+ Providers]
        end
        
        subgraph "Custom Model Layer"
            CustomModel[Custom Model<br/>Container.custom.cs]
        end
    end

    %% Azure Functions
    subgraph "Azure Functions"
        FunctionApp[Function App<br/>Program.cs]
        VehicleAccess[Vehicle Access Function<br/>VehicleAccessProcessorFunction]
        HealthCheck[Health Check Function<br/>HealthCheckFunction]
        FunctionServices[Function Services<br/>IoT, Sync, Access]
    end

    %% Testing Layer
    subgraph "Testing & Quality"
        UnitTests[Unit Tests<br/>UnitTests/]
        IntegrationTests[Integration Tests<br/>TestsLayer/]
        CypressTests[Cypress E2E Tests<br/>cypress/]
        VitestTests[Vitest Component Tests<br/>Vitest/]
    end

    %% Supporting Services
    subgraph "Supporting Services"
        LocaleTranslator[Locale Translator<br/>Translation Service]
        DailyTaskRunner[Daily Task Runner<br/>Scheduled Jobs]
        Scripts[Build Scripts<br/>Scripts/]
    end

    %% Key Business Entities
    subgraph "Core Business Entities"
        Vehicle[Vehicle<br/>VehicleDataObject]
        Driver[Driver<br/>DriverDataObject]
        Customer[Customer<br/>CustomerDataObject]
        Department[Department<br/>DepartmentDataObject]
        Site[Site<br/>SiteDataObject]
        Person[Person<br/>PersonDataObject]
        Module[Module<br/>ModuleDataObject]
        Firmware[Firmware<br/>FirmwareDataObject]
    end

    %% Technology Stack
    subgraph "Technology Stack"
        ASPNetCore[ASP.NET Core 6+]
        NHibernateORM[NHibernate ORM]
        EntityFramework[Entity Framework Core]
        AzureFunctions[Azure Functions]
        SQLServer[SQL Server]
        RedisCache[Redis Cache]
        ApplicationInsights[Application Insights]
        Swagger[Swagger/OpenAPI]
        Cypress[Cypress E2E Testing]
        Vitest[Vitest Component Testing]
    end

    %% Dependencies and Relationships
    WebApp --> ApiControllers
    WebApp --> EntityControllers
    WebApp --> ServiceConfig
    WebApp --> CustomContainer
    WebApp --> CustomMiddleware
    WebApp --> CustomSecurity

    ApiControllers --> BusinessComponents
    EntityControllers --> BusinessComponents
    BusinessComponents --> BusinessInterfaces
    BusinessComponents --> CustomBusiness

    BusinessComponents --> DataObjects
    BusinessComponents --> DataProviders
    BusinessComponents --> NHibernate

    DataObjects --> SQL
    DataProviders --> SQL
    NHibernate --> SQL

    CustomProviders --> CustomExtensions
    CustomExtensions --> DataProviders

    ModelComponents --> DataObjects
    ModelProviders --> DataProviders

    FunctionApp --> FunctionServices
    FunctionServices --> SQL
    FunctionServices --> AzureIoT
    FunctionServices --> AzureBlob

    Vehicle --> Driver
    Vehicle --> Customer
    Vehicle --> Department
    Vehicle --> Site
    Vehicle --> Module
    Vehicle --> Firmware

    Driver --> Person
    Department --> Customer
    Site --> Customer

    %% Technology Stack Dependencies
    WebApp --> ASPNetCore
    ApiControllers --> Swagger
    DataProviders --> NHibernateORM
    FunctionApp --> AzureFunctions
    DataObjects --> SQLServer
    WebApp --> RedisCache
    WebApp --> ApplicationInsights

    %% Testing Dependencies
    UnitTests --> BusinessComponents
    IntegrationTests --> FunctionServices
    CypressTests --> WebApp
    VitestTests --> CustomWeb

    %% External System Connections
    CustomSecurity --> AzureIoT
    BusinessComponents --> AzureBlob
    CustomBusiness --> RedisCache
    WebApp --> AppInsights

    %% Styling
    classDef generatedCode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef customCode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef externalSystem fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef businessEntity fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef technology fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef testing fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    %% Apply styles
    class WebApp,ApiControllers,EntityControllers,ServiceConfig,BusinessComponents,BusinessInterfaces,DataObjects,DataProviders,NHibernate,ModelComponents,ModelProviders generatedCode
    class CustomWeb,CustomStyles,CustomContainer,CustomMiddleware,CustomSecurity,CustomBusiness,CustomTests,CustomDataObjects,CustomProviders,CustomExtensions,CustomModel customCode
    class SQL,Redis,AzureIoT,AzureBlob,AppInsights externalSystem
    class Vehicle,Driver,Customer,Department,Site,Person,Module,Firmware businessEntity
    class ASPNetCore,NHibernateORM,EntityFramework,AzureFunctions,SQLServer,RedisCache,ApplicationInsights,Swagger,Cypress,Vitest technology
    class UnitTests,IntegrationTests,CypressTests,VitestTests testing 